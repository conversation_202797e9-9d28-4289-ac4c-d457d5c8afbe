import '../../resources/exports/index.dart';

import '../../view_model/extra_documents/extra_documents_controller.dart';
import 'document_type_drop_down.dart';

class ExtraDocuments extends GetView<ExtraDocumentsController> {
  const ExtraDocuments({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: const CustomAppBar(title: Strings.EXTRA_DOCUMENTS, backallow: true),
      body: GetBuilder<ExtraDocumentsController>(
        id: 'page_update',
        builder: (_) {
          return DocumentTypeDropDown();
        },
      ),
    );
  }
}
