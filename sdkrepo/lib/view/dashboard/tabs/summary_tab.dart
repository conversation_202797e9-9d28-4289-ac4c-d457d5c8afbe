import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';

class SummaryTab extends GetView<DashboardController> {
  const SummaryTab({super.key});

  bool isNoDocumentVerified() {
    if (controller.sessiondata == null) return true;

    final data = controller.sessiondata!;

    // Check if any verification data exists
    bool hasLoginData = data.loginType?.isNotEmpty == true && data.emailOrPhone?.isNotEmpty == true;
    bool hasSelfie = data.selfie.isNotEmpty;
    bool hasDocuments = data.documents.isNotEmpty;
    bool hasNfc = data.nfc?.isNotEmpty == true;
    bool hasNfcImage = data.nfcImage.isNotEmpty;
    bool hasQuestions = data.questions.isNotEmpty;
    bool hasSignature = data.signature.isNotEmpty;
    bool hasAddress = data.locationData?.addressData?.address?.isNotEmpty == true;
    bool hasExtraDocuments = data.extradocuments.isNotEmpty;

    // Return true if NO verification data exists
    return !hasLoginData && !hasSelfie && !hasDocuments && !hasNfc && !hasNfcImage && !hasQuestions && !hasSignature && !hasAddress && !hasExtraDocuments;
  }

  Widget buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              "No Verification Data",
              style: context.headlineSmall.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              "You haven't completed any verification steps yet.",
              textAlign: TextAlign.center,
              style: context.bodyLarge.copyWith(
                color: Colors.grey[500],
                height: 1.5,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate back to dashboard to start verification
                controller.currentPageIndex.value = 0;
              },
              icon: const Icon(
                CupertinoIcons.chevron_left_circle,
                size: 24,
              ),
              label: const Text("Start Verification"),
              style: ElevatedButton.styleFrom(
                elevation: 0,
                backgroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey[300]!),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Automatically fetch summary data when this widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.sessiondata == null) {
        controller.getSummary();
      }
    });

    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: const CustomAppBar(title: Strings.Summary),
      body: GetBuilder<DashboardController>(
        id: 'page_update',
        builder: (_) {
          if (controller.sessiondata == null) {
            return const Center(
              child: CupertinoActivityIndicator(),
            );
          }

          // Show empty state if no verification data exists
          if (isNoDocumentVerified()) {
            return buildEmptyState(context);
          }

          return Scaffold(
            backgroundColor: const Color(0xffF9FDFF),
            body: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SpaceH24(),
                      // Login Verification Section
                      if (controller.sessiondata?.loginType?.isNotEmpty ?? false)
                        Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            gradient: const LinearGradient(
                              end: Alignment.topLeft,
                              begin: Alignment.bottomRight,
                              colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                            ),
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(38),
                            ),
                          ),
                          child: ExpansionTile(
                            // collapsedBackgroundColor: Color(0xffE8E0EB),
                            shape: Border.all(color: Colors.transparent),
                            title: Text(Strings.VERIFY_BY, style: context.labelLarge),
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text("Login Type: "),
                                    Text(controller.sessiondata!.loginType!, style: context.titleMedium),
                                  ],
                                ),
                              ),
                              const Divider(
                                endIndent: 16,
                                indent: 16,
                              ),
                              const SpaceH4(),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text("${controller.sessiondata!.loginType!}:"),
                                    Text(controller.sessiondata!.emailOrPhone!, style: context.titleMedium),
                                  ],
                                ),
                              ),
                              const SpaceH16()
                            ],
                          ),
                        ),

                      // Selfie Verification Section
                      if (controller.sessiondata?.selfie.isNotEmpty ?? false)
                        Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            gradient: const LinearGradient(
                              end: Alignment.topLeft,
                              begin: Alignment.bottomRight,
                              colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                            ),
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(38),
                            ),
                          ),
                          child: ExpansionTile(
                            shape: Border.all(color: Colors.transparent),
                            title: Text(Strings.SELFIE_VERIFICTION, style: context.labelLarge),
                            children: [
                              GestureDetector(
                                onTap: () => Get.toNamed(Routes.PHOTOVIEW, arguments: controller.sessiondata!.selfie[0].documentPath),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                  child: ImageService.image(
                                    controller.sessiondata!.selfie[0].documentPath,
                                    borderRadius: 12.0,
                                    imageHeight: 174,
                                    imageWidth: double.maxFinite,
                                  ),
                                ),
                              ),
                              const SpaceH16()
                            ],
                          ),
                        ),

                      // Document Verification Section
                      if (controller.sessiondata?.documents.isNotEmpty ?? false)
                        Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            gradient: const LinearGradient(
                              end: Alignment.topLeft,
                              begin: Alignment.bottomRight,
                              colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                            ),
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(38),
                            ),
                          ),
                          child: ExpansionTile(
                            shape: Border.all(color: Colors.transparent),
                            title: Text(Strings.DOCUMENT_VERIFICATION, style: context.labelLarge),
                            children: [
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: [
                                    ...controller.sessiondata!.documents.map(
                                      (data) => Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                        child: Column(
                                          children: [
                                            GestureDetector(
                                              onTap: () => Get.toNamed(Routes.PHOTOVIEW, arguments: data.documentPath),
                                              child: ImageService.image(
                                                data.documentPath,
                                                borderRadius: 12.0,
                                                imageHeight: 174,
                                                imageWidth: 174,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SpaceH16()
                            ],
                          ),
                        ),

                      // NFC Verification Section
                      if (controller.sessiondata?.nfc?.isNotEmpty ?? false)
                        GestureDetector(
                          onTap: () => Get.toNamed(Routes.NFC_DETAILS),
                          child: Container(
                            margin: const EdgeInsets.only(bottom: 20),
                            clipBehavior: Clip.antiAlias,
                            decoration: ShapeDecoration(
                              gradient: const LinearGradient(
                                end: Alignment.topLeft,
                                begin: Alignment.bottomRight,
                                colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                              ),
                              shape: ContinuousRectangleBorder(
                                borderRadius: BorderRadius.circular(38),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          Strings.NFC_VERIFICATION,
                                          style: context.labelLarge.copyWith(
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        const SpaceH8(),
                                        Text(
                                          "Tap to view NFC details",
                                          style: context.bodyMedium.copyWith(
                                            color: Colors.black54,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(alpha: 0.3),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.arrow_forward_ios,
                                      size: 16,
                                      color: Colors.black54,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                      // Questions Section
                      if (controller.sessiondata?.questions.isNotEmpty ?? false)
                        Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            gradient: const LinearGradient(
                              end: Alignment.topLeft,
                              begin: Alignment.bottomRight,
                              colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                            ),
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(38),
                            ),
                          ),
                          child: ExpansionTile(
                            shape: Border.all(color: Colors.transparent),
                            title: Text(Strings.QUESTIONS, style: context.labelLarge),
                            children: [
                              ...controller.sessiondata!.questions.asMap().entries.map(
                                (entry) {
                                  int index = entry.key;
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.stretch,
                                      children: [
                                        if (index != 0) const SpaceH16(),
                                        Text(
                                          '${entry.key + 1}: ${entry.value["question"]}',
                                          style: context.titleMedium,
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                                          margin: const EdgeInsets.only(top: 12),
                                          // decoration: BoxDecoration(
                                          //   borderRadius: BorderRadius.circular(100),
                                          // ),
                                          clipBehavior: Clip.antiAlias,
                                          decoration: ShapeDecoration(
                                            color: Colors.white.withValues(alpha: 0.32),
                                            shape: ContinuousRectangleBorder(
                                              borderRadius: BorderRadius.circular(18),
                                            ),
                                          ),
                                          child: Text(
                                            '${entry.value["answer_text"]}',
                                            style: context.titleMedium,
                                          ),
                                        ),
                                        if (index == 0) const SpaceH24(),
                                        if (index == 0) const Divider(color: Colors.white, height: 0.0, thickness: 1.2),
                                      ],
                                    ),
                                  );
                                },
                              ),
                              const SpaceH16()
                            ],
                          ),
                        ),

                      // Signature Section
                      if (controller.sessiondata?.signature.isNotEmpty ?? false)
                        Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            gradient: const LinearGradient(
                              end: Alignment.topLeft,
                              begin: Alignment.bottomRight,
                              colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                            ),
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(38),
                            ),
                          ),
                          child: ExpansionTile(
                            shape: Border.all(color: Colors.transparent),
                            title: Text(Strings.SIGNATURE, style: context.labelLarge),
                            children: [
                              GestureDetector(
                                onTap: () => Get.toNamed(Routes.PHOTOVIEW, arguments: controller.sessiondata!.signature[0].documentPath),
                                child: ImageService.image(
                                  controller.sessiondata!.signature[0].documentPath,
                                  borderRadius: 12.0,
                                  imageHeight: 174,
                                  imageWidth: double.maxFinite,
                                ),
                              ),
                              const SpaceH16()
                            ],
                          ),
                        ),

                      // Address Section
                      if (controller.sessiondata?.locationData?.addressData?.address?.isNotEmpty ?? false)
                        Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            gradient: const LinearGradient(
                              end: Alignment.topLeft,
                              begin: Alignment.bottomRight,
                              colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                            ),
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(38),
                            ),
                          ),
                          child: ExpansionTile(
                            shape: Border.all(color: Colors.transparent),
                            title: Text(Strings.ADDRESS, style: context.labelLarge),
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                                child: Text(controller.sessiondata!.locationData!.addressData!.address ?? "", style: context.titleMedium),
                              ),
                              const SpaceH4(),
                              SizedBox(
                                height: 400.0,
                                width: double.maxFinite,
                                child: GoogleMap(
                                  mapType: MapType.normal,
                                  markers: {
                                    Marker(
                                      markerId: const MarkerId("location"),
                                      position: LatLng(
                                        controller.sessiondata!.locationData!.addressData!.lat! as double,
                                        controller.sessiondata!.locationData!.addressData!.lng! as double,
                                      ),
                                    ),
                                  },
                                  initialCameraPosition: CameraPosition(
                                    target: LatLng(
                                      controller.sessiondata!.locationData!.addressData!.lat! as double,
                                      controller.sessiondata!.locationData!.addressData!.lng! as double,
                                    ),
                                    zoom: 14.0,
                                  ),
                                  onMapCreated: (GoogleMapController mapController) {},
                                ),
                              ),
                              const SpaceH16()
                            ],
                          ),
                        ),

                      // Extra Documents Section
                      if (controller.sessiondata?.extradocuments.isNotEmpty ?? false)
                        Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            gradient: const LinearGradient(
                              end: Alignment.topLeft,
                              begin: Alignment.bottomRight,
                              colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                            ),
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(38),
                            ),
                          ),
                          child: ExpansionTile(
                            shape: Border.all(color: Colors.transparent),
                            title: Text(Strings.Extra_DOCS_Summary, style: context.labelLarge),
                            children: [
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: [
                                    ...controller.sessiondata!.extradocuments.map(
                                      (data) => Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                        child: GestureDetector(
                                          onTap: () => Get.toNamed(Routes.PHOTOVIEW, arguments: data.documentPath),
                                          child: Column(
                                            children: [
                                              ImageService.image(
                                                data.documentPath,
                                                borderRadius: 12.0,
                                                imageHeight: 174,
                                                imageWidth: 174,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SpaceH16()
                            ],
                          ),
                        ),
                    ],
                  ),
                )),
          );
        },
      ),
    );
  }
}
