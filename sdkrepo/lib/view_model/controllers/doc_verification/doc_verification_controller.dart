import 'package:digital_onboarding/digital_onboarding.dart';
import 'package:example/view/common/InformationalScreen.dart';
import 'package:example/view/doc_verification/document_preview_screen.dart';

import '../../../resources/exports/index.dart';

enum DocType { front, back }

class DocVerificationController extends GetxController {
  Uint8List? image;
  DocType docType = DocType.front;
  VerifyDocModel? doc;

  Uint8List? doc1;
  Uint8List? doc2;

  void pickImage() async {
    if (docType == DocType.front) {
      image = await ImagePickerService.showAIPickerSheet(Get.context!);
    } else {
      image = await ImagePickerService.openFrame('national');
    }
    Get.back();
    await updateImage();
  }

  void setImage(Uint8List? image) async {
    this.image = image;
    await updateImage();
  }

  Future<void> updateImage() async {
    update(['doc_picture']);
  }

  Future<void> verify(bool isLast) async {
    GlobalHelper.showPopupLoader();

    if (image == null) {
      CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      return;
    }

    try {
      final XFile file = await GlobalHelper().getFileFromUint8List(image!, "doc");
      final VerifyDocModel? model = await DigitalOnboardingServices.submitDocument(
        file,
        step: docType == DocType.front ? StepEnum.documentFront : StepEnum.documentBack,
      );

      print(isLast);

      if (model?.verified ?? false) {
        if (isLast) {
          print("inside the check");
          // Get.offAllNamed(Routes.DASHBOARD);
          Get.offAllNamed(Routes.DASHBOARD);
          CustomSnackBar.successSnackBar(message: "Document verification completed!");
          print("befire return");
          return;
        }
        Get.back();

        if (model?.isBackRequired ?? false) {
          print("its firs time");
          Get.off(
            InformationalScreen(
              imageOption: InformationalImageOptions.scanDocument,
              title: "Scan Document Back",
              onSubmit: () async {
                Map<String, Object>? image;

                image = await ImagePickerService.pickCustomAIImage('national');

                Get.back<Map<String, Object>>(result: image);

                Uint8List? cropedImage;

                if (image != null) {
                  GlobalHelper.showPopupLoader();
                  final fileData = await (image['fileData'] as XFile).readAsBytes();
                  final rectData = (image['rectData'] as double);
                  final cardType = (image['cardType'] as String);
                  final cropbytes = await ImagePickerService.cropImage(
                    fileData,
                    rectData,
                    Get.context!.deviceWidth,
                    cardType,
                  );
                  cropedImage = cropbytes;
                }

                Get.back();

                setImage(cropedImage);

                Get.to(const DocumentPreviewScreen(isLast: true));
              },
            ),
          );

          docType = DocType.back;
          image = null;
          updateImage();
          CustomSnackBar.successSnackBar(message: "Front side verification completed!");
          return;
        }
        Get.back();
      } else {
        print("🔍 DEBUG: Verification failed");
        return CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      }

      // Get.offAllNamed(Routes.DASHBOARD);
    } on DigitalOnboardingException {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
      Get.back();
    } catch (e) {
      CustomSnackBar.errorSnackBar(message: Strings.SOMETHING_WENT_WRONG);
      Get.back();
    }
  }

  Future<void> onUpdate() async {
    if (doc1 == null) {
      CustomSnackBar.errorSnackBar(message: Strings.IMAGE_FIRST);
      return;
    }

    if (doc1 != null) {
      try {
        XFile file = await GlobalHelper().getFileFromUint8List(doc1!, "Document1");
        await DigitalOnboardingServices.submitDocument(file, step: StepEnum.documentFront);
        Get.offAllNamed(Routes.DASHBOARD);
      } on DigitalOnboardingException {}
    }

    if (doc2 != null) {
      try {
        XFile file = await GlobalHelper().getFileFromUint8List(doc2!, "Document2");
        await DigitalOnboardingServices.submitDocument(file, step: StepEnum.documentFront);
        Get.offAllNamed(Routes.DASHBOARD);
      } on DigitalOnboardingException {}
    }
  }

  @override
  void dispose() {
    doc1 = null;
    doc2 = null;
    super.dispose();
  }
}
