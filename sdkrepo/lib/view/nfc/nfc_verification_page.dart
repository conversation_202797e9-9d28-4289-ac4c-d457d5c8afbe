import '../../resources/exports/index.dart';
import '../../view_model/services/nfc/nfc_scanning_service.dart';

class NFCVerificationPage extends GetView<NFCController> {
  const NFCVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(
        title: "NFC Verification",
        backallow: true,
      ),
      body: Column(
        children: [
          // Information Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.primary.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "NFC Verification",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const SpaceH8(),
                const Text(
                  "Place your document on the back of your phone to scan the NFC chip.",
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.black,
                  ),
                ),
                const SpaceH16(),

                // Display entered MRZ data
                GetBuilder<NFCController>(
                  id: 'mrz_data_display',
                  builder: (_) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Document Information:",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.black,
                          ),
                        ),
                        const SpaceH8(),
                        _buildDataRow("Name", controller.nameController.text),
                        _buildDataRow("Gender", controller.selectedGender == 'M' ? 'Male' : 'Female'),
                        _buildDataRow("Country", controller.countryCodeController.text),
                        _buildDataRow("Date of Birth", controller.dateOfBirthController.text),
                        _buildDataRow("Expiry Date", controller.expiryDateController.text),
                        _buildDataRow("Document Number", controller.documentNumberController.text),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),

          // NFC Scanner Section
          Expanded(
            child: NFCScanningService.buildMRZScanner(
              onMessage: (textData) async {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(textData)),
                );
              },
              onMrz: (mrzResult) async {
                // This won't be called in NFC verification mode
                // but we need to provide it for the scanner
              },
              onScan: (mrzData, imagedata) async {
                if (mrzData != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text("NFC Data Read Successfully! Uploading..."),
                    ),
                  );

                  // Combine manual MRZ data with NFC data
                  final combinedData = controller.getCombinedMRZData(mrzData);
                  await controller.onVerify(combinedData, imagedata);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text("NFC Data Empty"),
                    ),
                  );
                }
              },
            ),
          ),

          // Action Buttons
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                CustomButton.solid(
                  backgroundColor: AppColors.primary,
                  textColor: AppColors.white,
                  text: "Start NFC Scanning",
                  onTapAsync: () async {
                    NFCScanningService.startScanning();
                  },
                  radius: Sizes.RADIUS_12,
                  constraints: const BoxConstraints(minHeight: 55),
                ),
                const SpaceH16(),
                CustomButton.outline(
                  borderColor: AppColors.greyShade3,
                  textColor: AppColors.black,
                  text: "Edit Document Details",
                  onTapAsync: () async {
                    Get.back(); // Go back to manual form
                  },
                  radius: Sizes.RADIUS_12,
                  constraints: const BoxConstraints(minHeight: 55),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              "$label:",
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.greyShade2,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value ?? "Not provided",
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
