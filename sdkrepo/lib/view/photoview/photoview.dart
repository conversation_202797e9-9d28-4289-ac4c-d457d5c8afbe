import '../../resources/exports/index.dart';
import 'package:photo_view/photo_view.dart';

class PhotoViewScreen extends StatelessWidget with PortraitModeMixin {
  static const String routeName = '/photoview';

  const PhotoViewScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final String imageurl = Get.arguments as String;
    return Scaffold(
        appBar: const CustomAppBar(title: "Preview", backallow: true),
        body: Center(
          child: PhotoView(
            imageProvider: CachedNetworkImageProvider(imageurl),
            minScale: PhotoViewComputedScale.contained * 0.8,
            maxScale: PhotoViewComputedScale.covered * 2,
            initialScale: PhotoViewComputedScale.covered,
            backgroundDecoration: const BoxDecoration(color: Colors.white),
          ),
        ));
  }
}
