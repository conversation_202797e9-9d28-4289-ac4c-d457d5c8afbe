import 'package:flutter/cupertino.dart';

import '../../resources/exports/index.dart';

class QAScreen extends GetView<QAController> {
  const QAScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: const CustomAppBar(title: Strings.QUESTIONS, backallow: true),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: CircularProgressIndicator(color: AppColors.primary),
          );
        }

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: SingleChildScrollView(
            child: Column(
              children: [
                const SpaceH32(),
                // Progress indicator
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 20.0),
                  decoration: ShapeDecoration(
                    color: const Color(0xffE9EFF1),
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: GetBuilder<QAController>(
                    id: 'progress_update',
                    builder: (_) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Questions Progress',
                            style: context.titleMedium.copyWith(
                              color: AppColors.black,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '${controller.getAnsweredQuestionsCount()} / ${controller.numberOfQuestions}',
                            style: context.titleMedium.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
                const SpaceH16(),

                // Questions ListView
                ListView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.zero,
                  physics: const BouncingScrollPhysics(),
                  itemCount: controller.questionsList?.length ?? 0,
                  itemBuilder: (context, index) {
                    final question = controller.questionsList?[index];
                    if (question == null) return const SizedBox.shrink();

                    return QuestionCard(
                      questionModel: question,
                      questionIndex: index + 1,
                      isLast: index == (controller.questionsList?.length ?? 0) - 1,
                    );
                  },
                ),
                const SpaceH24(),
                GetBuilder<QAController>(
                  id: 'submit_btn',
                  builder: (_) {
                    final isAllAnswered = controller.areAllQuestionsAnswered();
                    return Opacity(
                      opacity: isAllAnswered ? 1.0 : 0.5,
                      child: CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: isAllAnswered ? controller.onSubmitAnswers : null,
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          width: double.maxFinite,
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            color: const Color(0xff8240DE),
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(24.0),
                            ),
                          ),
                          alignment: Alignment.center,
                          child: const DefaultTextStyle(
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                            child: Text(Strings.COMPLETE_KYC),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
