// import '../../../resources/exports/index.dart';

// class EmploymentType extends GetView<QAController> {
//   const EmploymentType({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         const SpaceH30(),
//         Text("Q3 : ${Strings.EMPLOYMENT_TYPE}", style: context.titleLarge),
//         const SpaceH120(),
//         GetBuilder<QAController>(
//           id: 'emp_type_dropdown',
//           builder: (_) {
//             return DropdownButtonHideUnderline(
//               child: DropdownButton2<String>(
//                 isExpanded: true,
//                 items: controller.empTypeitems
//                     .map(
//                       (String item) => DropdownMenuItem<String>(
//                         value: item,
//                         child: Text(
//                           item,
//                           style: context.titleMedium,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//                     )
//                     .toList(),
//                 value: controller.selectedEmpTypeItem,
//                 // onChanged: controller.togglePurpose,
//                 buttonStyleData: ButtonStyleData(
//                   height: 50,
//                   width: double.maxFinite,
//                   padding: const EdgeInsets.only(left: 14, right: 14),
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(14),
//                     border: Border.all(color: Colors.black26),
//                     color: AppColors.white,
//                   ),
//                   elevation: 2,
//                 ),
//                 iconStyleData: const IconStyleData(
//                   icon: Icon(Icons.arrow_forward_ios_outlined),
//                   iconSize: 14,
//                   iconEnabledColor: AppColors.black,
//                   iconDisabledColor: Colors.grey,
//                 ),
//                 dropdownStyleData: DropdownStyleData(
//                   maxHeight: 200,
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(14),
//                     color: AppColors.white,
//                   ),
//                   offset: const Offset(0, 0),
//                   scrollbarTheme: ScrollbarThemeData(
//                     radius: const Radius.circular(40),
//                     thickness: MaterialStateProperty.all<double>(6),
//                     thumbVisibility: MaterialStateProperty.all<bool>(true),
//                   ),
//                 ),
//                 menuItemStyleData: const MenuItemStyleData(
//                   height: 40,
//                   padding: EdgeInsets.symmetric(horizontal: 14),
//                 ),
//               ),
//             );
//           },
//         ),
//       ],
//     );
//   }
// }
