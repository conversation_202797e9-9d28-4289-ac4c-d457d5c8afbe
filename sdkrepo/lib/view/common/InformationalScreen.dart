import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';

enum InformationalImageOptions {
  selfie,
  scanDocument,
  nfcVerification,
  chooseDocumentToScan,
  scanFront,
  scanBack,
  signature,
}

class InformationalScreen extends StatelessWidget {
  const InformationalScreen({
    super.key,
    required this.imageOption,
    required this.title,
    this.submitTitle,
    required this.onSubmit,
    this.orderSubtitleList,
    this.actions,
  });

  final InformationalImageOptions imageOption;
  final String title;
  final String? submitTitle;
  final Function() onSubmit;
  final List<String>? orderSubtitleList;
  final List<Widget>? actions;

  String getImagePath() {
    switch (imageOption) {
      case InformationalImageOptions.selfie:
        return "assets/images/Serfle-guide.svg";
      case InformationalImageOptions.scanDocument:
        return "assets/images/scanDocument.svg";
      case InformationalImageOptions.scanFront:
        return "assets/images/scanDocument.svg";
      case InformationalImageOptions.nfcVerification:
        return "assets/images/NFC-Guide.svg";
      case InformationalImageOptions.chooseDocumentToScan:
        return "assets/images/chooseDocument.svg";
      case InformationalImageOptions.scanBack:
        return "assets/images/DocumentBackScan.svg";
      case InformationalImageOptions.signature:
        return "assets/images/signature_guide.svg";
    }
  }

  bool orderSubtitleListAvailable() {
    return orderSubtitleList != null && orderSubtitleList!.isNotEmpty;
  }

  bool actionsAvailable() {
    return actions != null && actions!.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      backgroundColor: const Color(0xffF9FDFF),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            SvgPicture.asset(getImagePath()),
            const SpaceH20(),
            Padding(
              padding: orderSubtitleListAvailable() ? const EdgeInsets.symmetric(horizontal: 40) : EdgeInsets.zero,
              child: Text(
                title,
                textAlign: orderSubtitleListAvailable() ? TextAlign.start : TextAlign.center,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            if (orderSubtitleListAvailable())
              Padding(
                padding: orderSubtitleListAvailable() ? const EdgeInsets.symmetric(horizontal: 40, vertical: 12) : EdgeInsets.zero,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: orderSubtitleList!.length,
                  itemBuilder: (context, index) {
                    final order = orderSubtitleList![index];
                    return Text(
                      "${index + 1}. $order",
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        fontWeight: FontWeight.w300,
                        height: 1.7,
                      ),
                    );
                  },
                ),
              ),
            if (actionsAvailable()) ...actions!,
            const Spacer(),
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: onSubmit,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                width: double.maxFinite,
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  color: const Color(0xff8240DE),
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadius.circular(24.0),
                  ),
                ),
                alignment: Alignment.center,
                child: DefaultTextStyle(
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  child: Text(submitTitle ?? "Proceed"),
                ),
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}
