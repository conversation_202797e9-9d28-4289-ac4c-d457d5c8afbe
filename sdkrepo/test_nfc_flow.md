# NFC Flow Testing Guide

## Overview
This document outlines how to test the new NFC verification flow that includes manual MRZ data entry and separate NFC scanning pages.

## New Flow Structure

### 1. Initial NFC Screen (`/nfc`)
- **File**: `lib/view/nfc/nfc.dart`
- **Purpose**: MRZ scanning with fallback option
- **Features**:
  - MRZ scanner using `NFCScanningService`
  - Auto-fills form fields when MRZ scanning succeeds
  - Shows success dialog with "Continue" and "Scan Again" options
  - "MRZ Scanning Failed? Enter Manually" button for fallback

### 2. Manual MRZ Form (`/manual_mrz_form`)
- **File**: `lib/view/nfc/manual_mrz_form.dart`
- **Purpose**: Manual entry when MRZ scanning fails
- **Features**:
  - Form fields for: Name, Gender, Country Code, Date of Birth, Expiry Date, Document Number
  - Date pickers for birth and expiry dates
  - Form validation
  - "Continue to NFC Verification" button

### 3. NFC Verification Page (`/nfc_verification`)
- **File**: `lib/view/nfc/nfc_verification_page.dart`
- **Purpose**: Dedicated NFC scanning after data entry
- **Features**:
  - Displays entered MRZ data
  - NFC scanner for chip reading
  - "Start NFC Scanning" button
  - "Edit Document Details" button to go back

## Key Components

### NFCScanningService
- **File**: `lib/view_model/services/nfc/nfc_scanning_service.dart`
- **Purpose**: Reusable MRZ scanning functionality
- **Solves**: The issue with `mrzCtrl.currentState?.scanning()` being widget-specific
- **Methods**:
  - `startScanning()`: Initiates MRZ scanning
  - `buildMRZScanner()`: Creates MRZ scanner widget
  - `resetController()`: Resets the controller

### Updated NFCController
- **File**: `lib/view_model/controllers/nfc/nfc_controller.dart`
- **New Features**:
  - Text controllers for manual data entry
  - Date selection methods
  - Form validation
  - Data combination logic (manual + NFC data)

## Testing Steps

### Test Case 1: Successful MRZ Scanning
1. Navigate to `/nfc`
2. Place document in camera frame
3. Verify MRZ data is scanned and displayed in dialog
4. Click "Continue" to go to NFC verification page
5. Verify data is displayed correctly
6. Click "Start NFC Scanning"
7. Place document on phone back for NFC reading

### Test Case 2: Failed MRZ Scanning (Manual Entry)
1. Navigate to `/nfc`
2. Click "MRZ Scanning Failed? Enter Manually"
3. Fill in all form fields manually
4. Click "Continue to NFC Verification"
5. Verify manually entered data is displayed
6. Click "Start NFC Scanning"
7. Place document on phone back for NFC reading

### Test Case 3: Edit Data Flow
1. Complete either Test Case 1 or 2 to reach NFC verification page
2. Click "Edit Document Details"
3. Verify navigation back to manual form (if came from manual) or NFC screen (if came from MRZ scan)
4. Make changes and continue again

## Routes Added
- `Routes.MANUAL_MRZ_FORM = "/manual_mrz_form"`
- `Routes.NFC_VERIFICATION = "/nfc_verification"`

## Files Modified/Created
- ✅ `lib/view/nfc/manual_mrz_form.dart` (NEW)
- ✅ `lib/view/nfc/nfc_verification_page.dart` (NEW)
- ✅ `lib/view_model/services/nfc/nfc_scanning_service.dart` (NEW)
- ✅ `lib/view_model/controllers/nfc/nfc_controller.dart` (UPDATED)
- ✅ `lib/view/nfc/nfc.dart` (UPDATED)
- ✅ `lib/resources/routes/routes.dart` (UPDATED)
- ✅ `lib/resources/routes/pages.dart` (UPDATED)
- ✅ `lib/resources/exports/views_export.dart` (UPDATED)

## Solution Summary
The implementation solves the original problem by:
1. **Extracting MRZ scanning logic** into a reusable service (`NFCScanningService`)
2. **Creating separate pages** for manual entry and NFC verification
3. **Making scanning function accessible** across different pages through the service
4. **Providing fallback flow** when MRZ scanning fails
5. **Maintaining data consistency** between manual entry and auto-filled data
