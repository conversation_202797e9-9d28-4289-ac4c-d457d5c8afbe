import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';

class DocumentPreviewScreen extends GetView<DocVerificationController> {
  const DocumentPreviewScreen({
    super.key,
    this.isLast,
  });
  final isLast;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: GetBuilder<DocVerificationController>(
              id: 'doc_picture',
              builder: (context) {
                if (controller.image == null) {
                  return const Center(
                    child: Text("Sorry we have Problem to load image"),
                  );
                }
                return Container(
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(24.0),
                    ),
                  ),
                  child: ImageService.image(
                    controller.image ?? 'https://iqid.yocat.net/landing-assets/images/logo-w.png',
                    borderRadius: 0,
                    imageHeight: 250,
                    imageWidth: double.maxFinite,
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Get.back();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 11),
                    width: double.maxFinite,
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      shape: ContinuousRectangleBorder(
                        borderRadius: BorderRadius.circular(24.0),
                        side: const BorderSide(color: Color(0xff8240DE)),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: const DefaultTextStyle(
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      child: Text(
                        "Retry",
                        style: TextStyle(
                          color: Color(0xff8240DE),
                        ),
                      ),
                    ),
                  ),
                ).expanded(),
                const SpaceW16(),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () async => controller.verify(isLast),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    width: double.maxFinite,
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      color: const Color(0xff8240DE),
                      shape: ContinuousRectangleBorder(
                        borderRadius: BorderRadius.circular(24.0),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: const DefaultTextStyle(
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      child: Text(" Verify"),
                    ),
                  ),
                ).expanded(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
