import '../../resources/exports/index.dart';

class ManualMRZForm extends GetView<NFCController> {
  const ManualMRZForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(
        title: "Enter Document Details",
        backallow: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "MRZ scanning failed. Please enter the document details manually:",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.black,
              ),
            ),
            const SpaceH20(),

            // Name Field
            CustomTextFormField(
              controller: controller.nameController,
              isRequired: true,
              height: Sizes.HEIGHT_20,
              labelText: "Full Name",
              labelColor: AppColors.black,
              textColor: AppColors.black,
              cursorColor: AppColors.black,
              errorColor: AppColors.error,
              enableBorderColor: AppColors.greyShade3,
              focusBorderColor: AppColors.primary,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.text,
            ),
            const SpaceH16(),

            // Gender Field
            GetBuilder<NFCController>(
              id: 'gender_dropdown',
              builder: (_) {
                return DropdownButtonFormField<String>(
                  value: controller.selectedGender,
                  decoration: InputDecoration(
                    labelText: "Gender",
                    labelStyle: const TextStyle(color: AppColors.black),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: AppColors.greyShade3),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: AppColors.primary),
                    ),
                  ),
                  items: ['M', 'F'].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value == 'M' ? 'Male' : 'Female'),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    controller.updateGender(newValue);
                  },
                );
              },
            ),
            const SpaceH16(),

            // Country Code Field
            CustomTextFormField(
              controller: controller.countryCodeController,
              isRequired: true,
              height: Sizes.HEIGHT_20,
              labelText: "Country Code (e.g., USA, GBR)",
              labelColor: AppColors.black,
              textColor: AppColors.black,
              cursorColor: AppColors.black,
              errorColor: AppColors.error,
              enableBorderColor: AppColors.greyShade3,
              focusBorderColor: AppColors.primary,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.text,
            ),
            const SpaceH16(),

            // Date of Birth Field
            GestureDetector(
              onTap: () => controller.selectDateOfBirth(context),
              child: AbsorbPointer(
                child: CustomTextFormField(
                  controller: controller.dateOfBirthController,
                  isRequired: true,
                  height: Sizes.HEIGHT_20,
                  labelText: "Date of Birth",
                  labelColor: AppColors.black,
                  textColor: AppColors.black,
                  cursorColor: AppColors.black,
                  errorColor: AppColors.error,
                  enableBorderColor: AppColors.greyShade3,
                  focusBorderColor: AppColors.primary,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.datetime,
                  suffixIcon: Icons.calendar_today,
                ),
              ),
            ),
            const SpaceH16(),

            // Expiry Date Field
            GestureDetector(
              onTap: () => controller.selectExpiryDate(context),
              child: AbsorbPointer(
                child: CustomTextFormField(
                  controller: controller.expiryDateController,
                  isRequired: true,
                  height: Sizes.HEIGHT_20,
                  labelText: "Expiry Date",
                  labelColor: AppColors.black,
                  textColor: AppColors.black,
                  cursorColor: AppColors.black,
                  errorColor: AppColors.error,
                  enableBorderColor: AppColors.greyShade3,
                  focusBorderColor: AppColors.primary,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.datetime,
                  suffixIcon: Icons.calendar_today,
                ),
              ),
            ),
            const SpaceH16(),

            // Document Number Field
            CustomTextFormField(
              controller: controller.documentNumberController,
              isRequired: true,
              height: Sizes.HEIGHT_20,
              labelText: "Document Number",
              labelColor: AppColors.black,
              textColor: AppColors.black,
              cursorColor: AppColors.black,
              errorColor: AppColors.error,
              enableBorderColor: AppColors.greyShade3,
              focusBorderColor: AppColors.primary,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.text,
            ),
            const SpaceH32(),

            // Submit Button
            GetBuilder<NFCController>(
              id: 'submit_button',
              builder: (_) {
                return CustomButton.solid(
                  backgroundColor: AppColors.primary,
                  textColor: AppColors.white,
                  text: "Continue to NFC Verification",
                  onTapAsync: () async {
                    await controller.proceedToNFCVerification();
                  },
                  radius: Sizes.RADIUS_12,
                  constraints: const BoxConstraints(minHeight: 55),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
