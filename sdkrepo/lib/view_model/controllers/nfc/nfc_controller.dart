import 'package:digital_onboarding/digital_onboarding.dart';

import '../../../resources/exports/index.dart';

class NFCController extends GetxController {
  String? base64Image;

  // Controllers for manual MRZ data entry
  late TextEditingController nameController;
  late TextEditingController countryCodeController;
  late TextEditingController dateOfBirthController;
  late TextEditingController expiryDateController;
  late TextEditingController documentNumberController;

  String? selectedGender;
  DateTime? selectedDateOfBirth;
  DateTime? selectedExpiryDate;

  @override
  void onInit() {
    super.onInit();
    _initializeControllers();
  }

  void _initializeControllers() {
    nameController = TextEditingController();
    countryCodeController = TextEditingController();
    dateOfBirthController = TextEditingController();
    expiryDateController = TextEditingController();
    documentNumberController = TextEditingController();
  }

  @override
  void onClose() {
    nameController.dispose();
    countryCodeController.dispose();
    dateOfBirthController.dispose();
    expiryDateController.dispose();
    documentNumberController.dispose();
    super.onClose();
  }

  void updateGender(String? gender) {
    selectedGender = gender;
    update(['gender_dropdown']);
  }

  Future<void> selectDateOfBirth(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDateOfBirth ?? DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != selectedDateOfBirth) {
      selectedDateOfBirth = picked;
      dateOfBirthController.text = DateFormat('yyyy-MM-dd').format(picked);
    }
  }

  Future<void> selectExpiryDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedExpiryDate ?? DateTime.now().add(const Duration(days: 365)), // 1 year from now
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)), // 10 years from now
    );
    if (picked != null && picked != selectedExpiryDate) {
      selectedExpiryDate = picked;
      expiryDateController.text = DateFormat('yyyy-MM-dd').format(picked);
    }
  }

  bool _validateManualForm() {
    if (nameController.text.trim().isEmpty) {
      CustomSnackBar.errorSnackBar(message: "Please enter full name");
      return false;
    }
    if (selectedGender == null) {
      CustomSnackBar.errorSnackBar(message: "Please select gender");
      return false;
    }
    if (countryCodeController.text.trim().isEmpty) {
      CustomSnackBar.errorSnackBar(message: "Please enter country code");
      return false;
    }
    if (dateOfBirthController.text.trim().isEmpty) {
      CustomSnackBar.errorSnackBar(message: "Please select date of birth");
      return false;
    }
    if (expiryDateController.text.trim().isEmpty) {
      CustomSnackBar.errorSnackBar(message: "Please select expiry date");
      return false;
    }
    if (documentNumberController.text.trim().isEmpty) {
      CustomSnackBar.errorSnackBar(message: "Please enter document number");
      return false;
    }
    return true;
  }

  Future<void> proceedToNFCVerification() async {
    if (_validateManualForm()) {
      Get.toNamed(Routes.NFC_VERIFICATION);
    }
  }

  Map<String, dynamic> getCombinedMRZData(Map<String, dynamic>? nfcData) {
    // Combine manual MRZ data with NFC data
    final manualData = {
      'givenNames': nameController.text.trim(),
      'sex': selectedGender,
      'countryCode': countryCodeController.text.trim(),
      'birthDate': dateOfBirthController.text.trim(),
      'expiryDate': expiryDateController.text.trim(),
      'documentNumber': documentNumberController.text.trim(),
    };

    // Merge with NFC data if available
    if (nfcData != null) {
      return {...manualData, ...nfcData};
    }

    return manualData;
  }

  Future<void> onVerify(Map<String, dynamic>? mrzData, Uint8List? jpegImage) async {
    GlobalHelper.showPopupLoader();
    if (jpegImage != null) {
      base64Image = base64Encode(jpegImage);

      XFile file = await GlobalHelper().getFileFromUint8List(jpegImage, "nfc");

      try {
        print("NFC Data: $mrzData");
        await DigitalOnboardingServices.submitDocument(file, step: StepEnum.nfc, extraData: json.encode(mrzData));
        Get.back();
        Get.offAllNamed(Routes.DASHBOARD);
      } on DigitalOnboardingException catch (e) {
        Get.back();
        CustomSnackBar.errorSnackBar(message: e.message ?? "NFC verification failed");
      }
    }
  }
}
