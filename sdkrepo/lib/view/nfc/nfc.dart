import '../../resources/exports/index.dart';
import '../../view_model/services/nfc/nfc_scanning_service.dart';

class NFCScreen extends GetView<NFCController> {
  const NFCScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.primary.withOpacity(0.3)),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "MRZ Scanning",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                SpaceH8(),
                Text(
                  "Place your document in the camera frame to scan the MRZ (Machine Readable Zone).",
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.black,
                  ),
                ),
              ],
            ),
          ),
          // MRZ Scanner Section
          Positioned.fill(
            child: NFCScanningService.buildMRZScanner(
              onMessage: (textData) async {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(textData)),
                );
              },
              onMrz: (mrzResult) async {
                inspect(mrzResult);
                // Auto-fill the controller with MRZ data
                controller.nameController.text = mrzResult.givenNames.toString();
                controller.selectedGender = mrzResult.sex.name == 'MALE' ? 'M' : 'F';
                controller.countryCodeController.text = mrzResult.countryCode.toString();
                controller.dateOfBirthController.text = mrzResult.birthDate.toString();
                controller.expiryDateController.text = mrzResult.expiryDate.toString();
                controller.documentNumberController.text = mrzResult.documentNumber.toString();

                await showDialog(
                  context: context,
                  builder: (context) => Dialog(
                    insetPadding: const EdgeInsets.symmetric(horizontal: 10),
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'MRZ Data Scanned Successfully!',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                          const SpaceH16(),
                          Text('Name: ${mrzResult.givenNames}'),
                          Text('Gender: ${mrzResult.sex.name}'),
                          Text('Country Code: ${mrzResult.countryCode}'),
                          Text('Date of Birth: ${mrzResult.birthDate}'),
                          Text('Expiry Date: ${mrzResult.expiryDate}'),
                          Text('Document Number: ${mrzResult.documentNumber}'),
                          const SpaceH20(),
                          Row(
                            children: [
                              Expanded(
                                child: CustomButton.outline(
                                  borderColor: Colors.grey,
                                  textColor: AppColors.black,
                                  text: 'Scan Again',
                                  onTapAsync: () async {
                                    Navigator.pop(context);
                                    NFCScanningService.startScanning();
                                  },
                                  radius: Sizes.RADIUS_8,
                                ),
                              ),
                              const SpaceW16(),
                              Expanded(
                                child: CustomButton.solid(
                                  backgroundColor: AppColors.primary,
                                  textColor: AppColors.white,
                                  text: 'Continue',
                                  onTapAsync: () async {
                                    Navigator.pop(context);
                                    Get.toNamed(Routes.NFC_VERIFICATION);
                                  },
                                  radius: Sizes.RADIUS_8,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
              onScan: (mrzData, imagedata) async {
                if (mrzData != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text("NFC Data Read Successfully! Uploading..."),
                    ),
                  );
                  await controller.onVerify(mrzData, imagedata);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text("NFC Data Empty"),
                    ),
                  );
                }
              },
            ),
          ),

          // Action Buttons
          // Padding(
          //   padding: const EdgeInsets.all(20),
          //   child: Column(
          //     children: [
          //       CustomButton.outline(
          //         borderColor: AppColors.primary,
          //         textColor: AppColors.primary,
          //         text: "MRZ Scanning Failed? Enter Manually",
          //         onTapAsync: () async {
          //           Get.toNamed(Routes.MANUAL_MRZ_FORM);
          //         },
          //         radius: Sizes.RADIUS_12,
          //         constraints: const BoxConstraints(minHeight: 55),
          //       ),
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }
}
