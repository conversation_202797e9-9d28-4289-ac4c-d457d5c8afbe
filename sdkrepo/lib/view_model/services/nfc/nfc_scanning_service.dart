import 'package:mrz_scanner/mrz_scanner.dart';
import '../../../resources/exports/index.dart';

class NFCScanningService {
  static MRZController? _mrzController;

  static MRZController get mrzController {
    _mrzController ??= MRZController();
    return _mrzController!;
  }

  static void startScanning() {
    mrzController.currentState?.scanning();
  }

  static void stopScanning() {
    // Stop scanning functionality - implementation depends on MRZ scanner API
    // mrzController.currentState?.stop(); // This method might not exist
  }

  static void resetController() {
    _mrzController = null;
  }

  static Widget buildMRZScanner({
    required Function(String) onMessage,
    required Function(MRZResult) onMrz,
    required Function(Map<String, dynamic>?, Uint8List?) onScan,
  }) {
    return MRZScanner(
      controller: mrzController,
      onMessage: onMessage,
      onMrz: onMrz,
      onScan: onScan,
    );
  }
}
